/**
 * createTask 功能测试运行脚本
 * 单独运行 createTask 相关的测试用例
 */

const { TestEnvironment, testCreateTask } = require('./todo-tool.test')

/**
 * 运行 createTask 测试
 */
async function runCreateTaskTests() {
  console.log('开始 createTask 功能测试...\n')
  
  const env = new TestEnvironment()
  
  try {
    // 运行 createTask 测试
    await testCreateTask(env)
    
    // 生成测试报告
    const report = env.generateReport()
    
    console.log('\n=== createTask 测试报告 ===')
    console.log('测试摘要：', JSON.stringify(report.summary, null, 2))
    
    // 输出详细结果
    console.log('\n=== 详细测试结果 ===')
    report.testResults.forEach(result => {
      const status = result.passed ? '✓' : '✗'
      const duration = result.duration ? ` (${result.duration}ms)` : ''
      console.log(`${status} ${result.testName}: ${result.message}${duration}`)
    })
    
    // 统计结果
    const createTaskTests = report.testResults.filter(r => 
      r.testName.includes('创建任务') || 
      r.testName.includes('参数测试') || 
      r.testName.includes('优先级') ||
      r.testName.includes('数据结构')
    )
    
    console.log(`\n=== createTask 专项统计 ===`)
    console.log(`总测试数: ${createTaskTests.length}`)
    console.log(`通过数: ${createTaskTests.filter(r => r.passed).length}`)
    console.log(`失败数: ${createTaskTests.filter(r => !r.passed).length}`)
    console.log(`成功率: ${createTaskTests.length > 0 ? 
      (createTaskTests.filter(r => r.passed).length / createTaskTests.length * 100).toFixed(2) + '%' : '0%'}`)
    
    return report
    
  } catch (error) {
    console.error('createTask 测试执行失败：', error)
    return null
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runCreateTaskTests().then(report => {
    if (report) {
      console.log('\ncreateTask 测试完成！')
      const failedTests = report.testResults.filter(r => !r.passed).length
      process.exit(failedTests > 0 ? 1 : 0)
    } else {
      console.log('\ncreateTask 测试失败！')
      process.exit(1)
    }
  })
}

module.exports = {
  runCreateTaskTests
}
