/**
 * 测试运行脚本
 * 统一执行所有测试用例
 */

const { runTests } = require('./todo-tool.test')
const { runPerformanceComparison } = require('./performance-comparison.test')

/**
 * 测试套件管理器
 */
class TestSuite {
  constructor() {
    this.results = []
    this.startTime = Date.now()
  }

  /**
   * 运行单个测试套件
   * @param {string} suiteName - 测试套件名称
   * @param {Function} testFunction - 测试函数
   * @returns {object} 测试结果
   */
  async runSuite(suiteName, testFunction) {
    console.log(`\n${'='.repeat(60)}`)
    console.log(`开始运行测试套件: ${suiteName}`)
    console.log(`${'='.repeat(60)}`)
    
    const suiteStartTime = Date.now()
    
    try {
      const result = await testFunction()
      const duration = Date.now() - suiteStartTime
      
      const suiteResult = {
        suiteName,
        success: true,
        duration,
        result,
        timestamp: new Date().toISOString()
      }
      
      this.results.push(suiteResult)
      
      console.log(`\n✓ 测试套件 "${suiteName}" 完成 (${duration}ms)`)
      return suiteResult
      
    } catch (error) {
      const duration = Date.now() - suiteStartTime
      
      const suiteResult = {
        suiteName,
        success: false,
        duration,
        error: error.message,
        timestamp: new Date().toISOString()
      }
      
      this.results.push(suiteResult)
      
      console.error(`\n✗ 测试套件 "${suiteName}" 失败 (${duration}ms):`, error.message)
      return suiteResult
    }
  }

  /**
   * 生成综合测试报告
   * @returns {object} 综合报告
   */
  generateComprehensiveReport() {
    const totalDuration = Date.now() - this.startTime
    const successfulSuites = this.results.filter(r => r.success).length
    const failedSuites = this.results.length - successfulSuites
    
    return {
      summary: {
        totalSuites: this.results.length,
        successfulSuites,
        failedSuites,
        successRate: this.results.length > 0 ? 
          (successfulSuites / this.results.length * 100).toFixed(2) + '%' : '0%',
        totalDuration: totalDuration + 'ms',
        timestamp: new Date().toISOString()
      },
      suiteResults: this.results,
      recommendations: this.generateRecommendations()
    }
  }

  /**
   * 生成改进建议
   * @returns {Array} 建议列表
   */
  generateRecommendations() {
    const recommendations = []
    
    // 检查失败的测试套件
    const failedSuites = this.results.filter(r => !r.success)
    if (failedSuites.length > 0) {
      recommendations.push({
        type: 'error',
        message: `有 ${failedSuites.length} 个测试套件失败，需要修复相关问题`,
        suites: failedSuites.map(s => s.suiteName)
      })
    }
    
    // 检查性能表现
    const performanceSuite = this.results.find(r => r.suiteName === '性能对比测试')
    if (performanceSuite && performanceSuite.success) {
      const comparisons = performanceSuite.result.comparisons
      if (comparisons) {
        const avgImprovement = (comparisons.getTasks.improvement + comparisons.getBatchData.improvement) / 2
        
        if (avgImprovement >= 30) {
          recommendations.push({
            type: 'success',
            message: `性能提升达标: 平均提升 ${avgImprovement.toFixed(1)}%，超过目标 30%`
          })
        } else {
          recommendations.push({
            type: 'warning',
            message: `性能提升未达标: 平均提升 ${avgImprovement.toFixed(1)}%，低于目标 30%`
          })
        }
      }
    }
    
    // 检查缓存效果
    const functionalSuite = this.results.find(r => r.suiteName === '功能测试')
    if (functionalSuite && functionalSuite.success) {
      const stats = functionalSuite.result.performanceStats
      if (stats && stats.cacheHitRate) {
        const hitRate = parseFloat(stats.cacheHitRate)
        if (hitRate > 50) {
          recommendations.push({
            type: 'success',
            message: `缓存效果良好: 命中率 ${stats.cacheHitRate}`
          })
        } else {
          recommendations.push({
            type: 'info',
            message: `缓存命中率较低: ${stats.cacheHitRate}，可考虑优化缓存策略`
          })
        }
      }
    }
    
    return recommendations
  }

  /**
   * 打印测试报告
   * @param {object} report - 测试报告
   */
  printReport(report) {
    console.log(`\n${'='.repeat(80)}`)
    console.log('综合测试报告')
    console.log(`${'='.repeat(80)}`)
    
    console.log('\n📊 测试摘要:')
    console.log(`  总测试套件数: ${report.summary.totalSuites}`)
    console.log(`  成功套件数: ${report.summary.successfulSuites}`)
    console.log(`  失败套件数: ${report.summary.failedSuites}`)
    console.log(`  成功率: ${report.summary.successRate}`)
    console.log(`  总耗时: ${report.summary.totalDuration}`)
    
    console.log('\n📋 套件详情:')
    report.suiteResults.forEach(suite => {
      const status = suite.success ? '✓' : '✗'
      console.log(`  ${status} ${suite.suiteName} (${suite.duration}ms)`)
      if (!suite.success) {
        console.log(`    错误: ${suite.error}`)
      }
    })
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 改进建议:')
      report.recommendations.forEach((rec, index) => {
        const icon = rec.type === 'success' ? '✅' : 
                    rec.type === 'warning' ? '⚠️' : 
                    rec.type === 'error' ? '❌' : 'ℹ️'
        console.log(`  ${icon} ${rec.message}`)
        if (rec.suites) {
          console.log(`    相关套件: ${rec.suites.join(', ')}`)
        }
      })
    }
    
    console.log(`\n${'='.repeat(80)}`)
  }
}

/**
 * 主测试运行函数
 */
async function runAllTests() {
  console.log('TodoList API 迁移整合 - 综合测试')
  console.log('测试开始时间:', new Date().toISOString())
  
  const testSuite = new TestSuite()
  
  // 运行功能测试
  await testSuite.runSuite('功能测试', runTests)
  
  // 运行性能对比测试
  await testSuite.runSuite('性能对比测试', runPerformanceComparison)
  
  // 生成并打印综合报告
  const report = testSuite.generateComprehensiveReport()
  testSuite.printReport(report)
  
  // 保存测试报告到文件
  const fs = require('fs')
  const path = require('path')
  
  try {
    const reportPath = path.join(__dirname, 'test-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 详细报告已保存到: ${reportPath}`)
  } catch (error) {
    console.warn('保存测试报告失败:', error.message)
  }
  
  // 返回测试结果
  return {
    success: report.summary.failedSuites === 0,
    report
  }
}

/**
 * 验证迁移目标达成情况
 * @param {object} report - 测试报告
 * @returns {object} 验证结果
 */
function validateMigrationGoals(report) {
  const goals = {
    functionalCompatibility: false,  // 功能兼容性
    performanceImprovement: false,   // 性能提升 >= 30%
    cacheEffectiveness: false,       // 缓存机制有效
    errorHandling: false             // 错误处理正确
  }
  
  const functionalSuite = report.suiteResults.find(s => s.suiteName === '功能测试')
  if (functionalSuite && functionalSuite.success) {
    goals.functionalCompatibility = true
    goals.errorHandling = true
    
    // 检查缓存效果
    const stats = functionalSuite.result.performanceStats
    if (stats && parseFloat(stats.cacheHitRate) > 0) {
      goals.cacheEffectiveness = true
    }
  }
  
  const performanceSuite = report.suiteResults.find(s => s.suiteName === '性能对比测试')
  if (performanceSuite && performanceSuite.success) {
    const comparisons = performanceSuite.result.comparisons
    if (comparisons) {
      const avgImprovement = (comparisons.getTasks.improvement + comparisons.getBatchData.improvement) / 2
      if (avgImprovement >= 30) {
        goals.performanceImprovement = true
      }
    }
  }
  
  const achievedGoals = Object.values(goals).filter(Boolean).length
  const totalGoals = Object.keys(goals).length
  
  return {
    goals,
    achievedGoals,
    totalGoals,
    successRate: (achievedGoals / totalGoals * 100).toFixed(1) + '%',
    allGoalsAchieved: achievedGoals === totalGoals
  }
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests().then(({ success, report }) => {
    // 验证迁移目标
    const validation = validateMigrationGoals(report)
    
    console.log('\n🎯 迁移目标达成情况:')
    console.log(`  功能兼容性: ${validation.goals.functionalCompatibility ? '✅' : '❌'}`)
    console.log(`  性能提升: ${validation.goals.performanceImprovement ? '✅' : '❌'}`)
    console.log(`  缓存机制: ${validation.goals.cacheEffectiveness ? '✅' : '❌'}`)
    console.log(`  错误处理: ${validation.goals.errorHandling ? '✅' : '❌'}`)
    console.log(`  总体达成率: ${validation.successRate}`)
    
    if (validation.allGoalsAchieved) {
      console.log('\n🎉 所有迁移目标已达成！')
      process.exit(0)
    } else {
      console.log('\n⚠️ 部分迁移目标未达成，需要进一步优化')
      process.exit(success ? 0 : 1)
    }
  }).catch(error => {
    console.error('\n❌ 测试执行失败:', error)
    process.exit(1)
  })
}

module.exports = {
  TestSuite,
  runAllTests,
  validateMigrationGoals
}
