/**
 * 执行上下文管理模块
 *
 * 主要功能：
 * 1. 管理任务执行过程中的所有数据和状态信息
 * 2. 提供步骤结果的存储和检索功能
 * 3. 自动提取和分析上下文数据
 * 4. 支持智能项目匹配和关键词提取
 * 5. 维护执行过程的元数据信息
 *
 * 核心特性：
 * - 会话隔离：每个执行会话都有独立的上下文
 * - 智能提取：自动从步骤结果中提取有用信息
 * - 项目匹配：基于用户输入智能匹配目标项目
 * - 数据关联：建立步骤间的数据依赖关系
 *
 * 使用场景：
 * - 多步骤任务执行时的数据传递
 * - 动态参数解析时的数据源
 * - 执行结果的统计和分析
 * - 用户意图的上下文理解
 *
 * <AUTHOR> 开发团队
 * @version 1.3.0
 * @since 2024-01-01
 */

const {
    IntelligentExecutionPlanner
} = require('./planner')
const {
    executeRobustPlan
} = require('./executor')
const {
    globalPerformanceMonitor
} = require('./performance')
const {
    OpenAI
} = require('openai')
const {
    doubaoParams,
    system,
    model
} = require('./config.js')

class ExecutionContextManager {
    /**
     * 构造函数 - 初始化执行上下文管理器
     *
     * @param {string} sessionId - 会话唯一标识，用于区分不同的执行会话
     */
    constructor(sessionId) {
        this.sessionId = sessionId // 会话 ID，确保不同会话的数据隔离
        this.stepResults = new Map() // 存储各步骤的执行结果，key 为 stepId
        this.contextData = new Map() // 存储提取的上下文数据，key 为数据类型
        this.metadata = {
            startTime: Date.now(), // 执行开始时间戳
            currentStep: 0, // 当前执行步骤索引
            totalSteps: 0, // 总步骤数（在执行计划确定后设置）
        }
    }

    /**
     * 设置步骤执行结果
     * 存储指定步骤的执行结果，并可选择性地触发上下文数据提取
     *
     * @param {string} stepId - 步骤唯一标识，用于后续检索
     * @param {Object} result - 步骤执行结果，包含工具调用的返回数据
     * @param {Object} [entities={}] - AI 从用户输入中提取的实体，用于数据提取
     * @param {Object} [metadata={}] - 附加元数据，可选参数
     *
     * 功能特性：
     * - 自动添加时间戳和步骤索引
     * - 触发上下文数据的自动提取
     * - 支持元数据的扩展存储
     */
    setStepResult(stepId, result, entities = {}, metadata = {}) {
        this.stepResults.set(stepId, {
            result, // 原始执行结果
            metadata: {
                ...metadata, // 用户提供的元数据
                timestamp: Date.now(), // 记录结果存储时间
                stepIndex: this.metadata.currentStep, // 记录当前步骤索引
            },
        })
        // 基于 AI 提取的实体，从结果中提取有用的上下文数据
        this.extractContextData(stepId, result, entities)
    }

    /**
     * 获取步骤执行结果
     * 根据步骤 ID 检索之前存储的执行结果
     *
     * @param {string} stepId - 步骤唯一标识
     * @returns {Object|undefined} 步骤执行结果，不存在时返回 undefined
     *
     * 使用场景：
     * - 后续步骤需要引用前面步骤的结果
     * - 动态参数解析时获取数据源
     * - 执行结果的统计和分析
     */
    getStepResult(stepId) {
        return this.stepResults.get(stepId)?.result
    }

    /**
     * 设置上下文数据
     * 手动设置特定键的上下文数据
     *
     * @param {string} key - 数据键名
     * @param {any} value - 数据值
     *
     * 使用场景：
     * - 手动添加特定的上下文信息
     * - 覆盖自动提取的数据
     * - 添加计算得出的衍生数据
     */
    setContextData(key, value) {
        this.contextData.set(key, value)
    }

    /**
     * 获取上下文数据
     * 根据键名获取提取的上下文数据
     *
     * @param {string} key - 数据键名，如 'projects'、'tasks' 等
     * @returns {any} 数据值，不存在时返回 undefined
     *
     * 常用键名：
     * - 'projects': 项目列表数据
     * - 'tasks': 任务列表数据
     * - 'completedTasks': 已完成任务列表
     * - 'uncompletedTasks': 未完成任务列表
     */
    getContextData(key) {
        return this.contextData.get(key)
    }

    /**
     * 从步骤结果中提取有用的上下文数据
     * 根据 AI 提取的实体，自动识别项目信息、任务统计等关键数据
     * @param {string} stepId - 步骤标识（用于日志记录）
     * @param {Object} result - 步骤执行结果
     * @param {Object} entities - AI 从用户输入中提取的实体
     */
    extractContextData(stepId, result, entities) {
        // 处理项目列表数据
        if (entities.projectName && result.data && Array.isArray(result.data)) {
            const targetProject = this.findTargetProject(result.data, entities.projectName)
            if (targetProject) {
                this.setContextData('targetProject', targetProject)
                console.log(`从步骤 ${stepId} 提取到目标项目:`, targetProject.name)
            }
        }

        // 处理任务列表数据
        if (result.tasks && Array.isArray(result.tasks)) {
            this.setContextData('taskCount', result.tasks.length)
            this.setContextData(
                'uncompletedTasks',
                result.tasks.filter((t) => !t.completed)
            )
            console.log(`从步骤 ${stepId} 提取到任务统计：总数=${result.tasks.length}`)
        }
    }

    /**
     * 查找目标项目
     * 基于 AI 提取的项目名称，在项目列表中进行匹配
     *
     * 匹配算法：
     * 1. 优先进行精确匹配
     * 2. 如果精确匹配失败，则尝试模糊匹配（包含关系）
     * 3. 返回匹配度最高的项目
     *
     * @param {Array} projects - 项目列表
     * @param {string} targetProjectName - AI 提取的目标项目名称
     * @returns {Object|null} 匹配的项目对象，未匹配到时返回 null
     */
    findTargetProject(projects, targetProjectName) {
        if (!targetProjectName || projects.length === 0) {
            return null
        }

        const targetName = targetProjectName.toLowerCase()

        // 1. 精确匹配
        const exactMatch = projects.find((p) => p.name.toLowerCase() === targetName)
        if (exactMatch) {
            return exactMatch
        }

        // 2. 模糊匹配（包含关系）
        const fuzzyMatches = projects.filter((p) => p.name.toLowerCase().includes(targetName))
        if (fuzzyMatches.length > 0) {
            // 如果有多个模糊匹配，可以根据一定规则选择最优的，这里简单返回第一个
            return fuzzyMatches[0]
        }

        return null
    }
}

module.exports = {
    ExecutionContextManager,
}